package com.zsmall.product.entity.domain.vo.warningMessage;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.product.entity.domain.WarningMessage;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 预警消息视图对象 warning_message
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WarningMessage.class)
public class WarningMessageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 可见范围分销商/供应商/admin
     */
    @ExcelProperty(value = "可见范围")
    private String tenantType;

    /**
     * 业务类型
     */
    @ExcelProperty(value = "业务类型")
    private Integer businessType;

    /**
     * 内容
     */
    @ExcelProperty(value = "内容")
    private String content;

    /**
     * 是否已读
     */
    @ExcelProperty(value = "是否已读")
    private Integer isRead;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;


}
