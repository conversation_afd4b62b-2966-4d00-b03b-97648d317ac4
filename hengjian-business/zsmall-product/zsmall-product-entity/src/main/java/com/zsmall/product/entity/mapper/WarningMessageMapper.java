package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.WarningMessage;
import com.zsmall.product.entity.domain.bo.warningMessage.WarningMessageBo;
import com.zsmall.product.entity.domain.vo.warningMessage.WarningMessageVo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 预警消息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface WarningMessageMapper extends BaseMapperPlus<WarningMessage, WarningMessageVo> {

    /**
     * 根据租户类型和业务类型查询未读消息数量
     * @return 未读消息数量
     */
    Long countUnreadMessages();

    /**
     * 批量标记消息为已读
     * @param ids 消息ID列表
     * @return 更新数量
     */
    int batchMarkAsRead(@Param("ids") List<Long> ids, @Param("tenantId") String tenantId);

    /**
     * 根据租户类型查询消息列表
     * @param tenantType 租户类型
     * @return 消息列表
     */
    List<WarningMessageVo> selectByTenantType(@Param("tenantType") String tenantType);
    /**
     * 查询预警消息列表
     * @param page 分页对象
     * @param bo 查询条件
     * @return 分页结果
     */
    IPage<WarningMessageVo> getList(IPage<WarningMessageVo> page, WarningMessageBo bo);
    /**
     * 批量删除预警消息
     * @param ids 预警消息主键集合
     * @return 结果
     */
    int deleteWarningMessage(Collection<Long> ids);
}
