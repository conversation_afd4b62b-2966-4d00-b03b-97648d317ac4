package com.zsmall.product.entity.domain.bo.warningMessage;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.WarningMessage;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 预警消息业务对象 warning_message
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WarningMessage.class, reverseConvertGenerate = false)
public class WarningMessageBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空", groups = {AddGroup.class, EditGroup.class})
    private String title;

    /**
     * 可见范围分销商/供应商/admin
     */
    @NotBlank(message = "可见范围不能为空", groups = {AddGroup.class, EditGroup.class})
    private String tenantType;

    /**
     * 业务类型
     */
    @NotNull(message = "业务类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer businessType;

    private Date createTimeStart;

    private Date createTimeEnd;

    /**
     * 是否已读
     */
    private Integer isRead;

    private String tenantId;

}
