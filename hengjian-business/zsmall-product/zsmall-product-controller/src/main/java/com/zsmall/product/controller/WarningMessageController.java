package com.zsmall.product.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.web.core.BaseController;
import com.zsmall.product.biz.service.WarningMessageService;
import com.zsmall.product.entity.domain.bo.warningMessage.WarningMessageBo;
import com.zsmall.product.entity.domain.dto.stock.SkuStock;
import com.zsmall.product.entity.domain.vo.warningMessage.WarningMessageVo;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 预警消息
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/warning/message")
@Tag(name = "预警消息管理", description = "预警消息管理")
public class WarningMessageController extends BaseController {


    private final IProductSkuStockService productSkuStockService;
    @Resource
    private final WarningMessageService warningMessageService;

    /**
     * 查询预警消息列表
     */
    @SaCheckLogin
    @Operation(summary = "查询预警消息列表")
    @GetMapping("/list")
    public R<TableDataInfo<WarningMessageVo>> list(WarningMessageBo bo, PageQuery pageQuery) {
        bo.setTenantId(LoginHelper.getTenantId());
        return warningMessageService.queryPageList(bo, pageQuery);
    }


    /**
     * 删除预警消息
     */
    @Operation(summary = "删除预警消息")
    @Log(title = "预警消息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@Parameter(description = "主键串") @PathVariable List<Long> ids) {
        return warningMessageService.deleteWithValidByIds(ids);
    }

    /**
     * 获取未读消息数量
     */
    @Operation(summary = "获取未读消息数量")
    @GetMapping("/unread/count")
    public R<Long> getUnreadCount() {
        return warningMessageService.getUnreadCount();
    }

    /**
     * 批量标记消息为已读
     */
    @SaCheckLogin
    @Operation(summary = "批量标记消息为已读")
    @PutMapping("/mark-read")
    public R<Void> batchMarkAsRead(@RequestBody List<Long> ids) {
        return warningMessageService.batchMarkAsRead(ids);
    }

    /**
     * 获取当前用户的消息列表
     */
    @Operation(summary = "获取当前用户的消息列表")
    @GetMapping("/current")
    public R<List<WarningMessageVo>> getCurrentUserMessages() {
        return warningMessageService.getCurrentUserMessages();
    }



    /**
     * 初始化所有库存状态
     */
    @Operation(summary = "初始化所有SKU的代发库存状态")
    @PostMapping("/init/allSkuDropStockStatus")
    @SaIgnore
    public R initAllStockStatus() {
        try {
            List<SkuStock> skuStocks=productSkuStockService.getBaseMapper().getDropShippingAllStock(Set.of("All", "DropShippingOnly"));
            skuStocks.forEach(s->{
                if (s.getDropShippingStockTotal() == 0){
                    RedisUtils.setCacheObject(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS+s.getProductSkuCode(), 0);
                }else {
                    RedisUtils.setCacheObject(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS+s.getProductSkuCode(), 1);
                }
            });
            return R.ok("初始化完成");
        } catch (Exception e) {
            return R.fail("初始化失败: " + e.getMessage());
        }
    }
}
